package it.unicam.cs.ids.piattaforma_agricola_locale.dto.subordine;

import it.unicam.cs.ids.piattaforma_agricola_locale.model.common.TipoAcquistabile;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * DTO per le righe dei sub-ordini.
 * Rappresenta un singolo elemento all'interno di un sub-ordine.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RigaSubOrdineDTO {

    private Long idRigaSubOrdine;
    private TipoAcquistabile tipoAcquistabile;
    private Long idAcquistabile;
    private String nomeProdotto;
    private String descrizioneProdotto;
    private int quantitaOrdinata;
    private double prezzoUnitario;
    private double totaleRiga;
    private String noteRiga;

    /**
     * Costruttore di convenienza per i dati principali
     */
    public RigaSubOrdineDTO(Long idRigaSubOrdine, String nomeProdotto,
            int quantitaOrdinata, double prezzoUnitario) {
        this.idRigaSubOrdine = idRigaSubOrdine;
        this.nomeProdotto = nomeProdotto;
        this.quantitaOrdinata = quantitaOrdinata;
        this.prezzoUnitario = prezzoUnitario;
        this.totaleRiga = quantitaOrdinata * prezzoUnitario;
    }
}

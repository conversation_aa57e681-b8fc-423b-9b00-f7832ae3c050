package it.unicam.cs.ids.piattaforma_agricola_locale.model.ordine;

import jakarta.persistence.*;
import it.unicam.cs.ids.piattaforma_agricola_locale.model.common.Acquistabile;
import it.unicam.cs.ids.piattaforma_agricola_locale.model.common.TipoAcquistabile;
import it.unicam.cs.ids.piattaforma_agricola_locale.service.AcquistabileService;

/**
 * Rappresenta una singola riga all'interno di un sub-ordine.
 * Ogni riga corrisponde a un prodotto/pacchetto/evento specifico ordinato
 * da un determinato venditore.
 */
@Entity
@Table(name = "righe_sub_ordine")
public class RigaSubOrdine {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id_riga_sub_ordine")
    private Long idRigaSubOrdine;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "id_sub_ordine", nullable = false)
    private SubOrdine subOrdine;

    @Enumerated(EnumType.STRING)
    @Column(name = "tipo_acquistabile", nullable = false)
    private TipoAcquistabile tipoAcquistabile;

    @Column(name = "id_acquistabile", nullable = false)
    private Long idAcquistabile;

    @Transient
    private AcquistabileService acquistabileService;

    @Column(name = "quantita_ordinata", nullable = false)
    private int quantitaOrdinata;

    @Column(name = "prezzo_unitario", nullable = false)
    private double prezzoUnitario;

    @Column(name = "nome_prodotto", nullable = false)
    private String nomeProdotto;

    @Column(name = "descrizione_prodotto")
    private String descrizioneProdotto;

    @Column(name = "note_riga")
    private String noteRiga;

    public RigaSubOrdine() {
    }

    public RigaSubOrdine(SubOrdine subOrdine, Acquistabile acquistabile, int quantitaOrdinata, double prezzoUnitario) {
        this.subOrdine = subOrdine;
        this.tipoAcquistabile = TipoAcquistabile.fromAcquistabile(acquistabile);
        this.idAcquistabile = acquistabile.getId();
        this.quantitaOrdinata = quantitaOrdinata;
        this.prezzoUnitario = prezzoUnitario;
        this.nomeProdotto = acquistabile.getNome();
        this.descrizioneProdotto = acquistabile.getDescrizione();
    }

    /**
     * Costruttore che copia i dati da una RigaOrdine esistente
     */
    public RigaSubOrdine(SubOrdine subOrdine, RigaOrdine rigaOrdine) {
        this.subOrdine = subOrdine;
        this.tipoAcquistabile = rigaOrdine.getTipoAcquistabile();
        this.idAcquistabile = rigaOrdine.getIdAcquistabile();
        this.quantitaOrdinata = rigaOrdine.getQuantitaOrdinata();
        this.prezzoUnitario = rigaOrdine.getPrezzoUnitario();

        // Recupera le informazioni del prodotto se disponibili
        if (rigaOrdine.getAcquistabile() != null) {
            this.nomeProdotto = rigaOrdine.getAcquistabile().getNome();
            this.descrizioneProdotto = rigaOrdine.getAcquistabile().getDescrizione();
        }
    }

    // Getters and Setters
    public Long getIdRigaSubOrdine() {
        return idRigaSubOrdine;
    }

    public void setIdRigaSubOrdine(Long idRigaSubOrdine) {
        this.idRigaSubOrdine = idRigaSubOrdine;
    }

    public SubOrdine getSubOrdine() {
        return subOrdine;
    }

    public void setSubOrdine(SubOrdine subOrdine) {
        this.subOrdine = subOrdine;
    }

    public TipoAcquistabile getTipoAcquistabile() {
        return tipoAcquistabile;
    }

    public void setTipoAcquistabile(TipoAcquistabile tipoAcquistabile) {
        this.tipoAcquistabile = tipoAcquistabile;
    }

    public Long getIdAcquistabile() {
        return idAcquistabile;
    }

    public void setIdAcquistabile(Long idAcquistabile) {
        this.idAcquistabile = idAcquistabile;
    }

    public AcquistabileService getAcquistabileService() {
        return acquistabileService;
    }

    public void setAcquistabileService(AcquistabileService acquistabileService) {
        this.acquistabileService = acquistabileService;
    }

    public int getQuantitaOrdinata() {
        return quantitaOrdinata;
    }

    public void setQuantitaOrdinata(int quantitaOrdinata) {
        this.quantitaOrdinata = quantitaOrdinata;
    }

    public double getPrezzoUnitario() {
        return prezzoUnitario;
    }

    public void setPrezzoUnitario(double prezzoUnitario) {
        this.prezzoUnitario = prezzoUnitario;
    }

    public String getNomeProdotto() {
        return nomeProdotto;
    }

    public void setNomeProdotto(String nomeProdotto) {
        this.nomeProdotto = nomeProdotto;
    }

    public String getDescrizioneProdotto() {
        return descrizioneProdotto;
    }

    public void setDescrizioneProdotto(String descrizioneProdotto) {
        this.descrizioneProdotto = descrizioneProdotto;
    }

    public String getNoteRiga() {
        return noteRiga;
    }

    public void setNoteRiga(String noteRiga) {
        this.noteRiga = noteRiga;
    }

    /**
     * Restituisce l'oggetto Acquistabile corrispondente a questa riga
     */
    public Acquistabile getAcquistabile() {
        if (acquistabileService != null) {
            return acquistabileService.findByTipoAndId(tipoAcquistabile, idAcquistabile);
        }
        return null;
    }

    /**
     * Calcola il totale per questa riga (prezzo * quantità)
     */
    public double getTotaleRiga() {
        return prezzoUnitario * quantitaOrdinata;
    }

    @Override
    public String toString() {
        return "RigaSubOrdine{" +
                "idRigaSubOrdine=" + idRigaSubOrdine +
                ", nomeProdotto='" + nomeProdotto + '\'' +
                ", quantitaOrdinata=" + quantitaOrdinata +
                ", prezzoUnitario=" + prezzoUnitario +
                ", totale=" + getTotaleRiga() +
                '}';
    }
}

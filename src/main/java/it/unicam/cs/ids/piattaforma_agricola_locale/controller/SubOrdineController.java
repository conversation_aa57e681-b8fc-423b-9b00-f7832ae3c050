package it.unicam.cs.ids.piattaforma_agricola_locale.controller;

import it.unicam.cs.ids.piattaforma_agricola_locale.dto.subordine.SubOrdineDetailDTO;
import it.unicam.cs.ids.piattaforma_agricola_locale.dto.subordine.SubOrdineSummaryDTO;
import it.unicam.cs.ids.piattaforma_agricola_locale.model.ordine.StatoSubOrdine;
import it.unicam.cs.ids.piattaforma_agricola_locale.model.ordine.SubOrdine;
import it.unicam.cs.ids.piattaforma_agricola_locale.model.utenti.Venditore;
import it.unicam.cs.ids.piattaforma_agricola_locale.service.interfaces.ISubOrdineService;
import it.unicam.cs.ids.piattaforma_agricola_locale.service.interfaces.IUtenteService;
import it.unicam.cs.ids.piattaforma_agricola_locale.service.mapper.SubOrdineMapper;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Controller per la gestione dei sub-ordini da parte dei venditori.
 * Questo controller permette ai venditori di visualizzare e gestire
 * solo i sub-ordini che contengono i loro prodotti.
 */
@RestController
@RequestMapping("/api/sub-ordini")
@RequiredArgsConstructor
@Slf4j
public class SubOrdineController {

    private final ISubOrdineService subOrdineService;
    private final IUtenteService utenteService;
    private final SubOrdineMapper subOrdineMapper;

    /**
     * Endpoint per permettere ai venditori di visualizzare i propri sub-ordini
     */
    @GetMapping("/venditori")
    @PreAuthorize("hasRole('VENDITORE')")
    public ResponseEntity<Page<SubOrdineSummaryDTO>> getSubOrdiniVenditore(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(defaultValue = "dataCreazione") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDirection,
            @RequestParam(required = false) String stato,
            Authentication authentication) {

        try {
            log.info("Getting sub-orders for vendor: {} - Page: {}, Size: {}, Filter: {}",
                    authentication.getName(), page, size, stato);

            String email = authentication.getName();
            Venditore venditore = (Venditore) utenteService.trovaUtentePerEmail(email)
                    .orElseThrow(() -> new RuntimeException("Venditore non trovato"));

            // Get vendor sub-orders (filtered by state if provided)
            List<SubOrdine> subOrdini;
            if (stato != null && !stato.trim().isEmpty()) {
                try {
                    StatoSubOrdine statoEnum = StatoSubOrdine.valueOf(stato.toUpperCase());
                    subOrdini = subOrdineService.trovaSubOrdiniPerVenditoreEStato(venditore, statoEnum);
                } catch (IllegalArgumentException e) {
                    log.warn("Invalid state filter: {}", stato);
                    return ResponseEntity.badRequest().build();
                }
            } else {
                subOrdini = subOrdineService.trovaSubOrdiniPerVenditore(venditore);
            }

            // Convert to DTOs
            List<SubOrdineSummaryDTO> subOrdiniDTO = subOrdini.stream()
                    .map(subOrdineMapper::toSummaryDTO)
                    .collect(Collectors.toList());

            // Apply sorting
            Sort sort = sortDirection.equalsIgnoreCase("desc") ? Sort.by(sortBy).descending()
                    : Sort.by(sortBy).ascending();

            // Manual pagination
            Pageable pageable = PageRequest.of(page, size, sort);
            int start = (int) pageable.getOffset();
            int end = Math.min((start + pageable.getPageSize()), subOrdiniDTO.size());

            List<SubOrdineSummaryDTO> pageContent = subOrdiniDTO.subList(start, end);
            Page<SubOrdineSummaryDTO> subOrdiniPage = new PageImpl<>(pageContent, pageable, subOrdiniDTO.size());

            return ResponseEntity.ok(subOrdiniPage);

        } catch (Exception e) {
            log.error("Error getting sub-orders for vendor: {}", authentication.getName(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Endpoint per visualizzare i dettagli di un sub-ordine specifico
     */
    @GetMapping("/{id}")
    @PreAuthorize("hasRole('VENDITORE')")
    public ResponseEntity<?> getSubOrdineById(
            @PathVariable Long id,
            Authentication authentication) {

        try {
            log.info("Getting sub-order details - SubOrderId: {}, Vendor: {}", id, authentication.getName());

            String email = authentication.getName();
            Venditore venditore = (Venditore) utenteService.trovaUtentePerEmail(email)
                    .orElseThrow(() -> new RuntimeException("Venditore non trovato"));

            Optional<SubOrdine> subOrdineOpt = subOrdineService.trovaSubOrdinePerID(id);
            if (subOrdineOpt.isEmpty()) {
                return ResponseEntity.notFound().build();
            }

            SubOrdine subOrdine = subOrdineOpt.get();

            // Check if the sub-order belongs to the authenticated vendor
            if (!subOrdineService.venditorePuoGestireSubOrdine(subOrdine, venditore)) {
                log.warn("Unauthorized access to sub-order - SubOrderId: {}, Vendor: {}, Owner: {}",
                        id, email, subOrdine.getVenditore().getEmail());
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                        .body(Map.of("error", "Accesso negato", "message",
                                "Non hai i permessi per visualizzare questo sub-ordine"));
            }

            SubOrdineDetailDTO subOrdineDTO = subOrdineMapper.toDetailDTO(subOrdine);
            return ResponseEntity.ok(subOrdineDTO);

        } catch (Exception e) {
            log.error("Error getting sub-order details - SubOrderId: {}, Vendor: {}", id, authentication.getName(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "Errore interno", "message",
                            "Si è verificato un errore durante il recupero del sub-ordine"));
        }
    }

    /**
     * Endpoint per aggiornare lo stato di un sub-ordine
     */
    @PutMapping("/{id}/stato")
    @PreAuthorize("hasRole('VENDITORE')")
    public ResponseEntity<?> aggiornaStatoSubOrdine(
            @PathVariable Long id,
            @Valid @RequestBody UpdateStatoRequest request,
            Authentication authentication) {

        try {
            log.info("Updating sub-order state - SubOrderId: {}, Vendor: {}, NewState: {}",
                    id, authentication.getName(), request.getNuovoStato());

            String email = authentication.getName();
            Venditore venditore = (Venditore) utenteService.trovaUtentePerEmail(email)
                    .orElseThrow(() -> new RuntimeException("Venditore non trovato"));

            Optional<SubOrdine> subOrdineOpt = subOrdineService.trovaSubOrdinePerID(id);
            if (subOrdineOpt.isEmpty()) {
                return ResponseEntity.notFound().build();
            }

            SubOrdine subOrdine = subOrdineOpt.get();

            // Check ownership
            if (!subOrdineService.venditorePuoGestireSubOrdine(subOrdine, venditore)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                        .body(Map.of("error", "Accesso negato", "message", "Non hai i permessi per questo sub-ordine"));
            }

            // Validate new state
            StatoSubOrdine nuovoStato;
            try {
                nuovoStato = StatoSubOrdine.valueOf(request.getNuovoStato().toUpperCase());
            } catch (IllegalArgumentException e) {
                return ResponseEntity.badRequest()
                        .body(Map.of("error", "Stato non valido", "message", "Lo stato specificato non è valido"));
            }

            // Update state
            SubOrdine subOrdineAggiornato = subOrdineService.aggiornaStatoSubOrdine(subOrdine, nuovoStato);

            log.info("Sub-order state updated successfully - SubOrderId: {}, Vendor: {}", id, email);
            return ResponseEntity.ok(Map.of(
                    "message", "Stato aggiornato con successo",
                    "nuovoStato", subOrdineAggiornato.getStatoSubOrdine()));

        } catch (Exception e) {
            log.error("Error updating sub-order state - SubOrderId: {}, Vendor: {}", id, authentication.getName(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "Errore interno", "message",
                            "Si è verificato un errore durante l'aggiornamento dello stato"));
        }
    }

    /**
     * Endpoint per avanzare un sub-ordine al prossimo stato logico
     */
    @PutMapping("/{id}/avanza")
    @PreAuthorize("hasRole('VENDITORE')")
    public ResponseEntity<?> avanzaStatoSubOrdine(
            @PathVariable Long id,
            Authentication authentication) {

        try {
            log.info("Advancing sub-order state - SubOrderId: {}, Vendor: {}", id, authentication.getName());

            String email = authentication.getName();
            Venditore venditore = (Venditore) utenteService.trovaUtentePerEmail(email)
                    .orElseThrow(() -> new RuntimeException("Venditore non trovato"));

            Optional<SubOrdine> subOrdineOpt = subOrdineService.trovaSubOrdinePerID(id);
            if (subOrdineOpt.isEmpty()) {
                return ResponseEntity.notFound().build();
            }

            SubOrdine subOrdine = subOrdineOpt.get();

            // Check ownership
            if (!subOrdineService.venditorePuoGestireSubOrdine(subOrdine, venditore)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                        .body(Map.of("error", "Accesso negato"));
            }

            // Advance state
            SubOrdine subOrdineAggiornato = subOrdineService.avanzaStatoSubOrdine(subOrdine);

            log.info("Sub-order state advanced successfully - SubOrderId: {}, Vendor: {}, NewState: {}",
                    id, email, subOrdineAggiornato.getStatoSubOrdine());
            return ResponseEntity.ok(Map.of(
                    "message", "Stato avanzato con successo",
                    "nuovoStato", subOrdineAggiornato.getStatoSubOrdine()));

        } catch (Exception e) {
            log.error("Error advancing sub-order state - SubOrderId: {}, Vendor: {}", id, authentication.getName(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "Errore interno"));
        }
    }

    /**
     * Endpoint per aggiungere un numero di tracking a un sub-ordine spedito
     */
    @PutMapping("/{id}/tracking")
    @PreAuthorize("hasRole('VENDITORE')")
    public ResponseEntity<?> aggiornaNumeroTracking(
            @PathVariable Long id,
            @Valid @RequestBody TrackingRequest request,
            Authentication authentication) {

        try {
            log.info("Updating tracking number - SubOrderId: {}, Vendor: {}", id, authentication.getName());

            String email = authentication.getName();
            Venditore venditore = (Venditore) utenteService.trovaUtentePerEmail(email)
                    .orElseThrow(() -> new RuntimeException("Venditore non trovato"));

            Optional<SubOrdine> subOrdineOpt = subOrdineService.trovaSubOrdinePerID(id);
            if (subOrdineOpt.isEmpty()) {
                return ResponseEntity.notFound().build();
            }

            SubOrdine subOrdine = subOrdineOpt.get();

            // Check ownership
            if (!subOrdineService.venditorePuoGestireSubOrdine(subOrdine, venditore)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                        .body(Map.of("error", "Accesso negato"));
            }

            // Update tracking number
            subOrdineService.aggiornaNumeroTracking(subOrdine, request.getNumeroTracking());

            log.info("Tracking number updated successfully - SubOrderId: {}, Vendor: {}", id, email);
            return ResponseEntity.ok(Map.of("message", "Numero di tracking aggiornato con successo"));

        } catch (Exception e) {
            log.error("Error updating tracking number - SubOrderId: {}, Vendor: {}", id, authentication.getName(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "Errore interno"));
        }
    }

    // Inner classes for request DTOs
    public static class UpdateStatoRequest {
        @NotBlank(message = "Il nuovo stato è obbligatorio")
        private String nuovoStato;

        public String getNuovoStato() {
            return nuovoStato;
        }

        public void setNuovoStato(String nuovoStato) {
            this.nuovoStato = nuovoStato;
        }
    }

    public static class TrackingRequest {
        @NotBlank(message = "Il numero di tracking è obbligatorio")
        private String numeroTracking;

        public String getNumeroTracking() {
            return numeroTracking;
        }

        public void setNumeroTracking(String numeroTracking) {
            this.numeroTracking = numeroTracking;
        }
    }
}

package it.unicam.cs.ids.piattaforma_agricola_locale.service.impl;

import it.unicam.cs.ids.piattaforma_agricola_locale.model.common.Acquistabile;
import it.unicam.cs.ids.piattaforma_agricola_locale.model.ordine.*;
import it.unicam.cs.ids.piattaforma_agricola_locale.model.repository.ISubOrdineRepository;
import it.unicam.cs.ids.piattaforma_agricola_locale.model.repository.IOrdineRepository;
import it.unicam.cs.ids.piattaforma_agricola_locale.model.utenti.Venditore;
import it.unicam.cs.ids.piattaforma_agricola_locale.service.AcquistabileService;
import it.unicam.cs.ids.piattaforma_agricola_locale.service.interfaces.ISubOrdineService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * Implementazione del servizio per la gestione dei sub-ordini.
 * Questo servizio gestisce la creazione e il ciclo di vita dei sub-ordini,
 * permettendo ai venditori di gestire indipendentemente i propri prodotti.
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class SubOrdineService implements ISubOrdineService {

    private final ISubOrdineRepository subOrdineRepository;
    private final AcquistabileService acquistabileService;
    private final IOrdineRepository ordineRepository;

    @Override
    public List<SubOrdine> creaSubOrdiniDaOrdine(Ordine ordine) {
        log.info("Creazione sub-ordini per ordine ID: {}", ordine.getIdOrdine());

        // Raggruppa le righe ordine per venditore
        Map<Venditore, List<RigaOrdine>> righePerVenditore = new HashMap<>();

        for (RigaOrdine riga : ordine.getRigheOrdine()) {
            // Recupera l'acquistabile per determinare il venditore
            Acquistabile acquistabile = acquistabileService.findByTipoAndId(
                    riga.getTipoAcquistabile(),
                    riga.getIdAcquistabile());

            if (acquistabile != null && acquistabile.getVenditore() != null) {
                Venditore venditore = acquistabile.getVenditore();
                righePerVenditore.computeIfAbsent(venditore, k -> new ArrayList<>()).add(riga);
            } else {
                log.warn("Impossibile determinare il venditore per la riga ordine ID: {}", riga.getIdRiga());
            }
        }

        // Crea un sub-ordine per ogni venditore
        List<SubOrdine> subOrdiniCreati = new ArrayList<>();

        for (Map.Entry<Venditore, List<RigaOrdine>> entry : righePerVenditore.entrySet()) {
            Venditore venditore = entry.getKey();
            List<RigaOrdine> righeVenditore = entry.getValue();

            SubOrdine subOrdine = new SubOrdine(ordine, venditore);

            // Crea le righe del sub-ordine
            for (RigaOrdine rigaOriginale : righeVenditore) {
                RigaSubOrdine rigaSubOrdine = new RigaSubOrdine(subOrdine, rigaOriginale);
                rigaSubOrdine.setAcquistabileService(acquistabileService);
                subOrdine.aggiungiRiga(rigaSubOrdine);
            }

            // Salva il sub-ordine
            SubOrdine subOrdineSalvato = subOrdineRepository.save(subOrdine);
            subOrdiniCreati.add(subOrdineSalvato);

            // Aggiungi il sub-ordine all'ordine principale
            ordine.aggiungiSubOrdine(subOrdineSalvato);

            log.info("Creato sub-ordine ID: {} per venditore: {}, con {} righe",
                    subOrdineSalvato.getIdSubOrdine(),
                    venditore.getEmail(),
                    righeVenditore.size());
        }

        log.info("Creati {} sub-ordini per l'ordine ID: {}", subOrdiniCreati.size(), ordine.getIdOrdine());
        return subOrdiniCreati;
    }

    @Override
    @Transactional(readOnly = true)
    public List<SubOrdine> trovaSubOrdiniPerVenditore(Venditore venditore) {
        log.debug("Ricerca sub-ordini per venditore: {}", venditore.getEmail());
        return subOrdineRepository.findByVenditoreOrderByDataCreazioneDesc(venditore);
    }

    @Override
    @Transactional(readOnly = true)
    public List<SubOrdine> trovaSubOrdiniPerVenditoreEStato(Venditore venditore, StatoSubOrdine stato) {
        log.debug("Ricerca sub-ordini per venditore: {} con stato: {}", venditore.getEmail(), stato);
        return subOrdineRepository.findByVenditoreAndStatoSubOrdine(venditore, stato);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<SubOrdine> trovaSubOrdinePerID(Long id) {
        log.debug("Ricerca sub-ordine per ID: {}", id);
        return subOrdineRepository.findById(id);
    }

    @Override
    public SubOrdine aggiornaStatoSubOrdine(SubOrdine subOrdine, StatoSubOrdine nuovoStato) {
        log.info("Aggiornamento stato sub-ordine ID: {} da {} a {}",
                subOrdine.getIdSubOrdine(),
                subOrdine.getStatoSubOrdine(),
                nuovoStato);

        subOrdine.setStatoSubOrdine(nuovoStato);

        SubOrdine subOrdineAggiornato = subOrdineRepository.save(subOrdine);

        // Coordina lo stato dell'ordine principale
        coordinaStatoOrdinePrincipale(subOrdineAggiornato.getOrdinePrincipale());

        log.info("Stato sub-ordine ID: {} aggiornato con successo", subOrdine.getIdSubOrdine());
        return subOrdineAggiornato;
    }

    @Override
    public SubOrdine avanzaStatoSubOrdine(SubOrdine subOrdine) {
        StatoSubOrdine statoAttuale = subOrdine.getStatoSubOrdine();

        if (statoAttuale.isStatoFinale()) {
            log.warn("Tentativo di avanzare sub-ordine ID: {} che è già in stato finale: {}",
                    subOrdine.getIdSubOrdine(), statoAttuale);
            return subOrdine;
        }

        StatoSubOrdine prossimoStato = statoAttuale.getProssimoStato();
        return aggiornaStatoSubOrdine(subOrdine, prossimoStato);
    }

    @Override
    @Transactional(readOnly = true)
    public boolean venditorePuoGestireSubOrdine(SubOrdine subOrdine, Venditore venditore) {
        return subOrdine.getVenditore().getIdUtente().equals(venditore.getIdUtente());
    }

    @Override
    public SubOrdine aggiornaNumeroTracking(SubOrdine subOrdine, String numeroTracking) {
        log.info("Aggiornamento numero tracking per sub-ordine ID: {}: {}",
                subOrdine.getIdSubOrdine(), numeroTracking);

        subOrdine.setNumeroTracking(numeroTracking);
        return subOrdineRepository.save(subOrdine);
    }

    @Override
    public SubOrdine aggiungiNoteVenditore(SubOrdine subOrdine, String note) {
        log.info("Aggiunta note venditore per sub-ordine ID: {}", subOrdine.getIdSubOrdine());

        subOrdine.setNoteVenditore(note);
        return subOrdineRepository.save(subOrdine);
    }

    @Override
    public SubOrdine salvaSubOrdine(SubOrdine subOrdine) {
        log.debug("Salvataggio sub-ordine ID: {}", subOrdine.getIdSubOrdine());
        return subOrdineRepository.save(subOrdine);
    }

    @Override
    @Transactional(readOnly = true)
    public boolean tuttiSubOrdiniCompletati(Ordine ordine) {
        List<SubOrdine> subOrdini = subOrdineRepository.findByOrdinePrincipaleId(ordine.getIdOrdine());

        if (subOrdini.isEmpty()) {
            log.warn("Nessun sub-ordine trovato per l'ordine ID: {}", ordine.getIdOrdine());
            return false;
        }

        boolean tuttiCompletati = subOrdini.stream().allMatch(SubOrdine::isCompletato);

        log.debug("Verifica completamento ordine ID: {} - Tutti completati: {}",
                ordine.getIdOrdine(), tuttiCompletati);

        return tuttiCompletati;
    }

    @Override
    @Transactional(readOnly = true)
    public double calcolaPercentualeCompletamento(Ordine ordine) {
        List<SubOrdine> subOrdini = subOrdineRepository.findByOrdinePrincipaleId(ordine.getIdOrdine());

        if (subOrdini.isEmpty()) {
            return 0.0;
        }

        long completati = subOrdini.stream()
                .mapToLong(sub -> sub.isCompletato() ? 1 : 0)
                .sum();

        double percentuale = (double) completati / subOrdini.size() * 100.0;

        log.debug("Percentuale completamento ordine ID: {} = {}%",
                ordine.getIdOrdine(), percentuale);

        return percentuale;
    }

    /**
     * Coordina lo stato dell'ordine principale in base agli stati dei sub-ordini
     */
    private void coordinaStatoOrdinePrincipale(Ordine ordinePrincipale) {
        log.debug("Coordinamento stato ordine principale ID: {}", ordinePrincipale.getIdOrdine());

        List<SubOrdine> subOrdini = subOrdineRepository.findByOrdinePrincipaleId(ordinePrincipale.getIdOrdine());

        if (subOrdini.isEmpty()) {
            log.warn("Nessun sub-ordine trovato per ordine ID: {}", ordinePrincipale.getIdOrdine());
            return;
        }

        // Determina il nuovo stato basato sui sub-ordini
        StatoCorrente nuovoStato = determinaStatoOrdineDaSubOrdini(subOrdini);

        if (nuovoStato != null && nuovoStato != ordinePrincipale.getStatoOrdine()) {
            log.info("Aggiornamento stato ordine ID: {} da {} a {}",
                    ordinePrincipale.getIdOrdine(),
                    ordinePrincipale.getStatoOrdine(),
                    nuovoStato);

            // Aggiorna lo stato dell'ordine
            ordinePrincipale.setStatoCorrente(nuovoStato);

            // Salva l'ordine aggiornato
            ordineRepository.save(ordinePrincipale);

            log.info("Stato ordine ID: {} aggiornato con successo", ordinePrincipale.getIdOrdine());
        }
    }

    /**
     * Determina il nuovo stato dell'ordine basato sugli stati dei sub-ordini.
     * La logica implementa il coordinamento degli stati per garantire che
     * l'ordine principale rifletta lo stato del sub-ordine meno avanzato
     * (quello che sta rallentando la consegna complessiva).
     */
    private StatoCorrente determinaStatoOrdineDaSubOrdini(List<SubOrdine> subOrdini) {
        if (subOrdini.isEmpty()) {
            return null; // Nessun cambiamento se non ci sono sub-ordini
        }

        // Se tutti i sub-ordini sono nello stesso stato, l'ordine assume quello stato
        if (subOrdini.stream().allMatch(s -> s.getStatoSubOrdine() == StatoSubOrdine.CONSEGNATO)) {
            return StatoCorrente.CONSEGNATO;
        }

        if (subOrdini.stream().allMatch(s -> s.getStatoSubOrdine() == StatoSubOrdine.SPEDITO)) {
            return StatoCorrente.SPEDITO;
        }

        if (subOrdini.stream().allMatch(s -> s.getStatoSubOrdine() == StatoSubOrdine.PRONTO_PER_SPEDIZIONE)) {
            return StatoCorrente.IN_ATTESA_DI_CONSEGNA;
        }

        if (subOrdini.stream().allMatch(s -> s.getStatoSubOrdine() == StatoSubOrdine.IN_LAVORAZIONE)) {
            return StatoCorrente.IN_LAVORAZIONE;
        }

        if (subOrdini.stream().allMatch(s -> s.getStatoSubOrdine() == StatoSubOrdine.ATTESA_LAVORAZIONE)) {
            return StatoCorrente.PRONTO_PER_LAVORAZIONE;
        }

        // Se ci sono stati misti, trova lo stato del sub-ordine meno avanzato
        // Questo rappresenta il "collo di bottiglia" che rallenta l'ordine complessivo
        StatoSubOrdine statoMenoAvanzato = trovaStatoMenoAvanzato(subOrdini);

        // Mappa lo stato del sub-ordine meno avanzato allo stato dell'ordine principale
        return mappaStatoSubOrdineAStatoCorrente(statoMenoAvanzato);
    }

    /**
     * Trova lo stato meno avanzato tra tutti i sub-ordini.
     * L'ordine di priorità (dal meno al più avanzato) è:
     * ATTESA_LAVORAZIONE < IN_LAVORAZIONE < PRONTO_PER_SPEDIZIONE < SPEDITO <
     * CONSEGNATO
     */
    private StatoSubOrdine trovaStatoMenoAvanzato(List<SubOrdine> subOrdini) {
        // Controlla in ordine di precedenza (dal meno avanzato al più avanzato)
        if (subOrdini.stream().anyMatch(s -> s.getStatoSubOrdine() == StatoSubOrdine.ATTESA_LAVORAZIONE)) {
            return StatoSubOrdine.ATTESA_LAVORAZIONE;
        }

        if (subOrdini.stream().anyMatch(s -> s.getStatoSubOrdine() == StatoSubOrdine.IN_LAVORAZIONE)) {
            return StatoSubOrdine.IN_LAVORAZIONE;
        }

        if (subOrdini.stream().anyMatch(s -> s.getStatoSubOrdine() == StatoSubOrdine.PRONTO_PER_SPEDIZIONE)) {
            return StatoSubOrdine.PRONTO_PER_SPEDIZIONE;
        }

        if (subOrdini.stream().anyMatch(s -> s.getStatoSubOrdine() == StatoSubOrdine.SPEDITO)) {
            return StatoSubOrdine.SPEDITO;
        }

        // Se arriviamo qui, tutti sono CONSEGNATO (ma questo caso è già gestito sopra)
        return StatoSubOrdine.CONSEGNATO;
    }

    /**
     * Mappa lo stato di un sub-ordine allo stato corrispondente dell'ordine
     * principale
     */
    private StatoCorrente mappaStatoSubOrdineAStatoCorrente(StatoSubOrdine statoSubOrdine) {
        switch (statoSubOrdine) {
            case ATTESA_LAVORAZIONE:
                return StatoCorrente.PRONTO_PER_LAVORAZIONE;
            case IN_LAVORAZIONE:
                return StatoCorrente.IN_LAVORAZIONE;
            case PRONTO_PER_SPEDIZIONE:
                return StatoCorrente.IN_ATTESA_DI_CONSEGNA;
            case SPEDITO:
                return StatoCorrente.SPEDITO;
            case CONSEGNATO:
                return StatoCorrente.CONSEGNATO;
            default:
                return StatoCorrente.IN_LAVORAZIONE; // Fallback
        }
    }
}

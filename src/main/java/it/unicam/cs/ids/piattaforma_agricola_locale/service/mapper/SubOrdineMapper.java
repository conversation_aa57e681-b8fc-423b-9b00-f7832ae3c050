package it.unicam.cs.ids.piattaforma_agricola_locale.service.mapper;

import it.unicam.cs.ids.piattaforma_agricola_locale.dto.subordine.RigaSubOrdineDTO;
import it.unicam.cs.ids.piattaforma_agricola_locale.dto.subordine.SubOrdineDetailDTO;
import it.unicam.cs.ids.piattaforma_agricola_locale.dto.subordine.SubOrdineSummaryDTO;
import it.unicam.cs.ids.piattaforma_agricola_locale.model.ordine.RigaSubOrdine;
import it.unicam.cs.ids.piattaforma_agricola_locale.model.ordine.SubOrdine;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Mapper per convertire entità SubOrdine in DTO e viceversa.
 * Gestisce la conversione tra i modelli di dominio e i DTO per l'API.
 */
@Component
@RequiredArgsConstructor
public class SubOrdineMapper {

    /**
     * Converte un SubOrdine in SubOrdineSummaryDTO
     */
    public SubOrdineSummaryDTO toSummaryDTO(SubOrdine subOrdine) {
        if (subOrdine == null) {
            return null;
        }

        SubOrdineSummaryDTO dto = new SubOrdineSummaryDTO();
        dto.setIdSubOrdine(subOrdine.getIdSubOrdine());
        dto.setIdOrdinePrincipale(subOrdine.getOrdinePrincipale().getIdOrdine());
        dto.setStatoSubOrdine(subOrdine.getStatoSubOrdine());
        dto.setDescrizioneStato(subOrdine.getStatoSubOrdine().getDescrizione());
        dto.setImportoSubOrdine(subOrdine.getImportoSubOrdine());
        dto.setDataCreazione(subOrdine.getDataCreazione());
        dto.setDataAggiornamento(subOrdine.getDataAggiornamento());
        dto.setNumeroTracking(subOrdine.getNumeroTracking());
        dto.setCompletato(subOrdine.isCompletato());
        dto.setPuoEssereProcessato(subOrdine.puoEssereProcessato());

        // Informazioni acquirente
        if (subOrdine.getOrdinePrincipale().getAcquirente() != null) {
            dto.setEmailAcquirente(subOrdine.getOrdinePrincipale().getAcquirente().getEmail());
            dto.setNomeAcquirente(subOrdine.getOrdinePrincipale().getAcquirente().getNome() +
                    " " + subOrdine.getOrdinePrincipale().getAcquirente().getCognome());
        }

        // Numero di righe e dettagli righe
        if (subOrdine.getRigheSubOrdine() != null) {
            List<RigaSubOrdineDTO> righeDTO = subOrdine.getRigheSubOrdine().stream()
                    .map(this::toRigaSubOrdineDTO)
                    .collect(Collectors.toList());
            dto.setRigheSubOrdine(righeDTO);
            dto.setNumeroRighe(righeDTO.size());
        } else {
            dto.setRigheSubOrdine(Collections.emptyList());
            dto.setNumeroRighe(0);
        }

        return dto;
    }

    /**
     * Converte un SubOrdine in SubOrdineDetailDTO
     */
    public SubOrdineDetailDTO toDetailDTO(SubOrdine subOrdine) {
        if (subOrdine == null) {
            return null;
        }

        SubOrdineDetailDTO dto = new SubOrdineDetailDTO();
        dto.setIdSubOrdine(subOrdine.getIdSubOrdine());
        dto.setIdOrdinePrincipale(subOrdine.getOrdinePrincipale().getIdOrdine());
        dto.setDataOrdineOriginale(subOrdine.getOrdinePrincipale().getDataOrdine());
        dto.setStatoSubOrdine(subOrdine.getStatoSubOrdine());
        dto.setDescrizioneStato(subOrdine.getStatoSubOrdine().getDescrizione());
        dto.setImportoSubOrdine(subOrdine.getImportoSubOrdine());
        dto.setDataCreazione(subOrdine.getDataCreazione());
        dto.setDataAggiornamento(subOrdine.getDataAggiornamento());
        dto.setNumeroTracking(subOrdine.getNumeroTracking());
        dto.setNoteVenditore(subOrdine.getNoteVenditore());
        dto.setCompletato(subOrdine.isCompletato());
        dto.setPuoEssereProcessato(subOrdine.puoEssereProcessato());
        dto.setPuoEssereSpedito(subOrdine.getStatoSubOrdine().puoEssereSpedito());

        // Informazioni acquirente
        if (subOrdine.getOrdinePrincipale().getAcquirente() != null) {
            dto.setEmailAcquirente(subOrdine.getOrdinePrincipale().getAcquirente().getEmail());
            dto.setNomeAcquirente(subOrdine.getOrdinePrincipale().getAcquirente().getNome() +
                    " " + subOrdine.getOrdinePrincipale().getAcquirente().getCognome());
            // Potresti aggiungere l'indirizzo se disponibile
            // dto.setIndirizzoConsegna(subOrdine.getOrdinePrincipale().getAcquirente().getIndirizzo());
        }

        // Converti le righe
        if (subOrdine.getRigheSubOrdine() != null) {
            List<RigaSubOrdineDTO> righeDTO = subOrdine.getRigheSubOrdine().stream()
                    .map(this::toRigaSubOrdineDTO)
                    .collect(Collectors.toList());
            dto.setRigheSubOrdine(righeDTO);
            dto.setNumeroRighe(righeDTO.size());
        } else {
            dto.setRigheSubOrdine(Collections.emptyList());
            dto.setNumeroRighe(0);
        }

        // Metadati per l'UI
        dto.setRichiedeAzione(!subOrdine.getStatoSubOrdine().isStatoFinale());
        dto.setProssimaAzionePossibile(determinaProssimaAzione(subOrdine.getStatoSubOrdine()));

        return dto;
    }

    /**
     * Converte una RigaSubOrdine in RigaSubOrdineDTO
     */
    public RigaSubOrdineDTO toRigaSubOrdineDTO(RigaSubOrdine rigaSubOrdine) {
        if (rigaSubOrdine == null) {
            return null;
        }

        RigaSubOrdineDTO dto = new RigaSubOrdineDTO();
        dto.setIdRigaSubOrdine(rigaSubOrdine.getIdRigaSubOrdine());
        dto.setTipoAcquistabile(rigaSubOrdine.getTipoAcquistabile());
        dto.setIdAcquistabile(rigaSubOrdine.getIdAcquistabile());
        dto.setNomeProdotto(rigaSubOrdine.getNomeProdotto());
        dto.setDescrizioneProdotto(rigaSubOrdine.getDescrizioneProdotto());
        dto.setQuantitaOrdinata(rigaSubOrdine.getQuantitaOrdinata());
        dto.setPrezzoUnitario(rigaSubOrdine.getPrezzoUnitario());
        dto.setTotaleRiga(rigaSubOrdine.getTotaleRiga());
        dto.setNoteRiga(rigaSubOrdine.getNoteRiga());

        return dto;
    }

    /**
     * Converte una lista di SubOrdine in lista di SubOrdineSummaryDTO
     */
    public List<SubOrdineSummaryDTO> toSummaryDTOList(List<SubOrdine> subOrdini) {
        if (subOrdini == null) {
            return Collections.emptyList();
        }

        return subOrdini.stream()
                .map(this::toSummaryDTO)
                .collect(Collectors.toList());
    }

    /**
     * Converte una lista di SubOrdine in lista di SubOrdineDetailDTO
     */
    public List<SubOrdineDetailDTO> toDetailDTOList(List<SubOrdine> subOrdini) {
        if (subOrdini == null) {
            return Collections.emptyList();
        }

        return subOrdini.stream()
                .map(this::toDetailDTO)
                .collect(Collectors.toList());
    }

    /**
     * Determina la prossima azione possibile basata sullo stato corrente
     */
    private String determinaProssimaAzione(
            it.unicam.cs.ids.piattaforma_agricola_locale.model.ordine.StatoSubOrdine stato) {
        switch (stato) {
            case ATTESA_LAVORAZIONE:
                return "Inizia lavorazione";
            case IN_LAVORAZIONE:
                return "Segna come pronto per spedizione";
            case PRONTO_PER_SPEDIZIONE:
                return "Spedisci";
            case SPEDITO:
                return "Conferma consegna";
            default:
                return "Nessuna azione richiesta";
        }
    }
}

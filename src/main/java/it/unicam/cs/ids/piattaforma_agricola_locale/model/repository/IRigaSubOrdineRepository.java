package it.unicam.cs.ids.piattaforma_agricola_locale.model.repository;

import it.unicam.cs.ids.piattaforma_agricola_locale.model.ordine.RigaSubOrdine;
import it.unicam.cs.ids.piattaforma_agricola_locale.model.ordine.SubOrdine;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Repository per la gestione delle righe dei sub-ordini nel database
 */
@Repository
public interface IRigaSubOrdineRepository extends JpaRepository<RigaSubOrdine, Long> {

    /**
     * Trova tutte le righe di un sub-ordine specifico
     * 
     * @param subOrdine Il sub-ordine
     * @return Lista delle righe del sub-ordine
     */
    List<RigaSubOrdine> findBySubOrdine(SubOrdine subOrdine);

    /**
     * Trova tutte le righe di un sub-ordine per ID
     * 
     * @param subOrdineId L'ID del sub-ordine
     * @return Lista delle righe del sub-ordine
     */
    @Query("SELECT r FROM RigaSubOrdine r WHERE r.subOrdine.idSubOrdine = :subOrdineId")
    List<RigaSubOrdine> findBySubOrdineId(@Param("subOrdineId") Long subOrdineId);

    /**
     * Calcola il totale di un sub-ordine sommando tutte le sue righe
     * 
     * @param subOrdineId L'ID del sub-ordine
     * @return Il totale calcolato
     */
    @Query("SELECT COALESCE(SUM(r.prezzoUnitario * r.quantitaOrdinata), 0.0) FROM RigaSubOrdine r WHERE r.subOrdine.idSubOrdine = :subOrdineId")
    Double calcolaTotaleSubOrdine(@Param("subOrdineId") Long subOrdineId);

    /**
     * Conta il numero di righe in un sub-ordine
     * 
     * @param subOrdineId L'ID del sub-ordine
     * @return Numero di righe nel sub-ordine
     */
    @Query("SELECT COUNT(r) FROM RigaSubOrdine r WHERE r.subOrdine.idSubOrdine = :subOrdineId")
    Long contaRigheSubOrdine(@Param("subOrdineId") Long subOrdineId);
}

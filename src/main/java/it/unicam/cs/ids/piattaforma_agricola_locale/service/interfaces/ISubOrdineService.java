package it.unicam.cs.ids.piattaforma_agricola_locale.service.interfaces;

import it.unicam.cs.ids.piattaforma_agricola_locale.model.ordine.Ordine;
import it.unicam.cs.ids.piattaforma_agricola_locale.model.ordine.StatoSubOrdine;
import it.unicam.cs.ids.piattaforma_agricola_locale.model.ordine.SubOrdine;
import it.unicam.cs.ids.piattaforma_agricola_locale.model.utenti.Venditore;

import java.util.List;
import java.util.Optional;

/**
 * Interfaccia per la gestione dei sub-ordini.
 * I sub-ordini permettono di suddividere un ordine principale
 * in base al venditore, consentendo a ogni venditore di gestire
 * solo i propri prodotti.
 */
public interface ISubOrdineService {

    /**
     * Crea automaticamente i sub-ordini a partire da un ordine principale.
     * Raggruppa le righe ordine per venditore e crea un sub-ordine per ciascuno.
     * 
     * @param ordine L'ordine principale da cui creare i sub-ordini
     * @return Lista dei sub-ordini creati
     */
    List<SubOrdine> creaSubOrdiniDaOrdine(Ordine ordine);

    /**
     * Trova tutti i sub-ordini di un venditore specifico
     * 
     * @param venditore Il venditore di cui trovare i sub-ordini
     * @return Lista dei sub-ordini del venditore
     */
    List<SubOrdine> trovaSubOrdiniPerVenditore(Venditore venditore);

    /**
     * Trova tutti i sub-ordini di un venditore con un determinato stato
     * 
     * @param venditore Il venditore
     * @param stato     Lo stato dei sub-ordini da trovare
     * @return Lista dei sub-ordini filtrati per stato
     */
    List<SubOrdine> trovaSubOrdiniPerVenditoreEStato(Venditore venditore, StatoSubOrdine stato);

    /**
     * Trova un sub-ordine per ID
     * 
     * @param id L'ID del sub-ordine
     * @return Optional contenente il sub-ordine se trovato
     */
    Optional<SubOrdine> trovaSubOrdinePerID(Long id);

    /**
     * Aggiorna lo stato di un sub-ordine
     * 
     * @param subOrdine  Il sub-ordine da aggiornare
     * @param nuovoStato Il nuovo stato
     * @return Il sub-ordine aggiornato
     */
    SubOrdine aggiornaStatoSubOrdine(SubOrdine subOrdine, StatoSubOrdine nuovoStato);

    /**
     * Avanza un sub-ordine al prossimo stato logico
     * 
     * @param subOrdine Il sub-ordine da far avanzare
     * @return Il sub-ordine aggiornato
     */
    SubOrdine avanzaStatoSubOrdine(SubOrdine subOrdine);

    /**
     * Verifica se un venditore può gestire un determinato sub-ordine
     * 
     * @param subOrdine Il sub-ordine
     * @param venditore Il venditore
     * @return true se il venditore può gestire il sub-ordine
     */
    boolean venditorePuoGestireSubOrdine(SubOrdine subOrdine, Venditore venditore);

    /**
     * Aggiorna il numero di tracking per un sub-ordine spedito
     * 
     * @param subOrdine      Il sub-ordine
     * @param numeroTracking Il numero di tracking
     * @return Il sub-ordine aggiornato
     */
    SubOrdine aggiornaNumeroTracking(SubOrdine subOrdine, String numeroTracking);

    /**
     * Aggiunge note del venditore a un sub-ordine
     * 
     * @param subOrdine Il sub-ordine
     * @param note      Le note da aggiungere
     * @return Il sub-ordine aggiornato
     */
    SubOrdine aggiungiNoteVenditore(SubOrdine subOrdine, String note);

    /**
     * Salva un sub-ordine nel database
     * 
     * @param subOrdine Il sub-ordine da salvare
     * @return Il sub-ordine salvato
     */
    SubOrdine salvaSubOrdine(SubOrdine subOrdine);

    /**
     * Verifica se tutti i sub-ordini di un ordine principale sono completati
     * 
     * @param ordine L'ordine principale
     * @return true se tutti i sub-ordini sono completati
     */
    boolean tuttiSubOrdiniCompletati(Ordine ordine);

    /**
     * Calcola la percentuale di completamento di un ordine basata sui sub-ordini
     * 
     * @param ordine L'ordine principale
     * @return Percentuale di completamento (0-100)
     */
    double calcolaPercentualeCompletamento(Ordine ordine);
}

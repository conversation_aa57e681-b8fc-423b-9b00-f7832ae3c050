package it.unicam.cs.ids.piattaforma_agricola_locale.model.repository;

import it.unicam.cs.ids.piattaforma_agricola_locale.model.ordine.StatoSubOrdine;
import it.unicam.cs.ids.piattaforma_agricola_locale.model.ordine.SubOrdine;
import it.unicam.cs.ids.piattaforma_agricola_locale.model.utenti.Venditore;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Repository per la gestione dei sub-ordini nel database
 */
@Repository
public interface ISubOrdineRepository extends JpaRepository<SubOrdine, Long> {

    /**
     * Trova tutti i sub-ordini di un venditore specifico
     * 
     * @param venditore Il venditore
     * @return Lista dei sub-ordini del venditore
     */
    List<SubOrdine> findByVenditore(Venditore venditore);

    /**
     * Trova tutti i sub-ordini di un venditore con un determinato stato
     * 
     * @param venditore Il venditore
     * @param stato     Lo stato dei sub-ordini
     * @return Lista dei sub-ordini filtrati
     */
    List<SubOrdine> findByVenditoreAndStatoSubOrdine(Venditore venditore, StatoSubOrdine stato);

    /**
     * Trova tutti i sub-ordini di un ordine principale
     * 
     * @param ordineId L'ID dell'ordine principale
     * @return Lista dei sub-ordini dell'ordine
     */
    @Query("SELECT s FROM SubOrdine s WHERE s.ordinePrincipale.idOrdine = :ordineId")
    List<SubOrdine> findByOrdinePrincipaleId(@Param("ordineId") Long ordineId);

    /**
     * Trova tutti i sub-ordini di un venditore ordinati per data di creazione
     * 
     * @param venditore Il venditore
     * @return Lista dei sub-ordini ordinati per data
     */
    List<SubOrdine> findByVenditoreOrderByDataCreazioneDesc(Venditore venditore);

    /**
     * Trova tutti i sub-ordini con un determinato stato
     * 
     * @param stato Lo stato dei sub-ordini
     * @return Lista dei sub-ordini con lo stato specificato
     */
    List<SubOrdine> findByStatoSubOrdine(StatoSubOrdine stato);

    /**
     * Conta il numero di sub-ordini di un venditore con un determinato stato
     * 
     * @param venditore Il venditore
     * @param stato     Lo stato
     * @return Numero di sub-ordini con lo stato specificato
     */
    Long countByVenditoreAndStatoSubOrdine(Venditore venditore, StatoSubOrdine stato);

    /**
     * Verifica se esistono sub-ordini per un determinato ordine principale
     * 
     * @param ordineId L'ID dell'ordine principale
     * @return true se esistono sub-ordini per l'ordine
     */
    @Query("SELECT COUNT(s) > 0 FROM SubOrdine s WHERE s.ordinePrincipale.idOrdine = :ordineId")
    boolean existsByOrdinePrincipaleId(@Param("ordineId") Long ordineId);

    /**
     * Trova i sub-ordini che sono pronti per la spedizione
     * 
     * @return Lista dei sub-ordini pronti per spedizione
     */
    @Query("SELECT s FROM SubOrdine s WHERE s.statoSubOrdine = 'PRONTO_PER_SPEDIZIONE'")
    List<SubOrdine> findSubOrdiniProntiPerSpedizione();

    /**
     * Trova i sub-ordini spediti che non hanno ancora un numero di tracking
     * 
     * @return Lista dei sub-ordini spediti senza tracking
     */
    @Query("SELECT s FROM SubOrdine s WHERE s.statoSubOrdine = 'SPEDITO' AND (s.numeroTracking IS NULL OR s.numeroTracking = '')")
    List<SubOrdine> findSubOrdiniSpediteSenzaTracking();
}

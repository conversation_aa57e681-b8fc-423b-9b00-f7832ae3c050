package it.unicam.cs.ids.piattaforma_agricola_locale.dto.subordine;

import it.unicam.cs.ids.piattaforma_agricola_locale.model.ordine.StatoSubOrdine;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * DTO per la visualizzazione sommaria dei sub-ordini.
 * Contiene le informazioni essenziali per le liste e panoramiche,
 * inclusi i dettagli degli elementi del sub-ordine.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SubOrdineSummaryDTO {

    private Long idSubOrdine;
    private Long idOrdinePrincipale;
    private String emailAcquirente;
    private String nomeAcquirente;
    private StatoSubOrdine statoSubOrdine;
    private String descrizioneStato;
    private double importoSubOrdine;
    private Date dataCreazione;
    private Date dataAggiornamento;
    private int numeroRighe;
    private String numeroTracking;
    private boolean completato;
    private boolean puoEssereProcessato;

    // Lista degli elementi del sub-ordine
    private List<RigaSubOrdineDTO> righeSubOrdine;

    /**
     * Costruttore di convenienza per i dati principali
     */
    public SubOrdineSummaryDTO(Long idSubOrdine, Long idOrdinePrincipale,
            StatoSubOrdine statoSubOrdine, double importoSubOrdine,
            Date dataCreazione, int numeroRighe) {
        this.idSubOrdine = idSubOrdine;
        this.idOrdinePrincipale = idOrdinePrincipale;
        this.statoSubOrdine = statoSubOrdine;
        this.descrizioneStato = statoSubOrdine.getDescrizione();
        this.importoSubOrdine = importoSubOrdine;
        this.dataCreazione = dataCreazione;
        this.numeroRighe = numeroRighe;
        this.completato = statoSubOrdine.isStatoFinale();
        this.puoEssereProcessato = statoSubOrdine.puoEssereLavorato();
    }
}

package it.unicam.cs.ids.piattaforma_agricola_locale.dto.subordine;

import it.unicam.cs.ids.piattaforma_agricola_locale.model.ordine.StatoSubOrdine;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * DTO per la visualizzazione dettagliata dei sub-ordini.
 * Contiene tutte le informazioni del sub-ordine incluse le righe.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SubOrdineDetailDTO {

    private Long idSubOrdine;
    private Long idOrdinePrincipale;
    private Date dataOrdineOriginale;

    // Informazioni acquirente
    private String emailAcquirente;
    private String nomeAcquirente;
    private String indirizzoConsegna;

    // Informazioni stato
    private StatoSubOrdine statoSubOrdine;
    private String descrizioneStato;
    private boolean completato;
    private boolean puoEssereProcessato;
    private boolean puoEssereSpedito;

    // Informazioni sub-ordine
    private double importoSubOrdine;
    private Date dataCreazione;
    private Date dataAggiornamento;
    private String numeroTracking;
    private String noteVenditore;

    // Righe del sub-ordine
    private List<RigaSubOrdineDTO> righeSubOrdine;
    private int numeroRighe;

    // Metadati
    private boolean richiedeAzione;
    private String prossimaAzionePossibile;

    public SubOrdineDetailDTO(Long idSubOrdine, Long idOrdinePrincipale,
            StatoSubOrdine statoSubOrdine, double importoSubOrdine,
            Date dataCreazione, List<RigaSubOrdineDTO> righeSubOrdine) {
        this.idSubOrdine = idSubOrdine;
        this.idOrdinePrincipale = idOrdinePrincipale;
        this.statoSubOrdine = statoSubOrdine;
        this.descrizioneStato = statoSubOrdine.getDescrizione();
        this.importoSubOrdine = importoSubOrdine;
        this.dataCreazione = dataCreazione;
        this.righeSubOrdine = righeSubOrdine;
        this.numeroRighe = righeSubOrdine != null ? righeSubOrdine.size() : 0;
        this.completato = statoSubOrdine.isStatoFinale();
        this.puoEssereProcessato = statoSubOrdine.puoEssereLavorato();
        this.puoEssereSpedito = statoSubOrdine.puoEssereSpedito();
        this.richiedeAzione = !statoSubOrdine.isStatoFinale();
        this.prossimaAzionePossibile = determinaProssimaAzione(statoSubOrdine);
    }

    private String determinaProssimaAzione(StatoSubOrdine stato) {
        switch (stato) {
            case ATTESA_LAVORAZIONE:
                return "Inizia lavorazione";
            case IN_LAVORAZIONE:
                return "Segna come pronto per spedizione";
            case PRONTO_PER_SPEDIZIONE:
                return "Spedisci";
            case SPEDITO:
                return "Conferma consegna";
            default:
                return "Nessuna azione richiesta";
        }
    }
}

package it.unicam.cs.ids.piattaforma_agricola_locale.model.ordine;

import jakarta.persistence.*;
import it.unicam.cs.ids.piattaforma_agricola_locale.model.utenti.Venditore;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Rappresenta un sub-ordine che contiene solo gli elementi di un singolo
 * venditore.
 * Questo permette ai venditori di gestire indipendentemente i propri prodotti
 * all'interno di un ordine più ampio che può coinvolgere più venditori.
 */
@Entity
@Table(name = "sub_ordini")
public class SubOrdine {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id_sub_ordine")
    private Long idSubOrdine;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "id_ordine_principale", nullable = false)
    private Ordine ordinePrincipale;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "id_venditore", nullable = false)
    private Venditore venditore;

    @OneToMany(mappedBy = "subOrdine", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<RigaSubOrdine> righeSubOrdine;

    @Column(name = "importo_sub_ordine", nullable = false)
    private double importoSubOrdine;

    @Enumerated(EnumType.STRING)
    @Column(name = "stato_sub_ordine", nullable = false)
    private StatoSubOrdine statoSubOrdine;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "data_creazione", nullable = false)
    private Date dataCreazione;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "data_aggiornamento")
    private Date dataAggiornamento;

    @Column(name = "numero_tracking")
    private String numeroTracking;

    @Column(name = "note_venditore")
    private String noteVenditore;

    public SubOrdine() {
        this.righeSubOrdine = new ArrayList<>();
        this.statoSubOrdine = StatoSubOrdine.ATTESA_LAVORAZIONE;
        this.dataCreazione = new Date();
        this.importoSubOrdine = 0.0;
    }

    public SubOrdine(Ordine ordinePrincipale, Venditore venditore) {
        this();
        this.ordinePrincipale = ordinePrincipale;
        this.venditore = venditore;
    }

    // Getters and Setters
    public Long getIdSubOrdine() {
        return idSubOrdine;
    }

    public void setIdSubOrdine(Long idSubOrdine) {
        this.idSubOrdine = idSubOrdine;
    }

    public Ordine getOrdinePrincipale() {
        return ordinePrincipale;
    }

    public void setOrdinePrincipale(Ordine ordinePrincipale) {
        this.ordinePrincipale = ordinePrincipale;
    }

    public Venditore getVenditore() {
        return venditore;
    }

    public void setVenditore(Venditore venditore) {
        this.venditore = venditore;
    }

    public List<RigaSubOrdine> getRigheSubOrdine() {
        return righeSubOrdine;
    }

    public void setRigheSubOrdine(List<RigaSubOrdine> righeSubOrdine) {
        this.righeSubOrdine = righeSubOrdine;
    }

    public double getImportoSubOrdine() {
        return importoSubOrdine;
    }

    public void setImportoSubOrdine(double importoSubOrdine) {
        this.importoSubOrdine = importoSubOrdine;
    }

    public StatoSubOrdine getStatoSubOrdine() {
        return statoSubOrdine;
    }

    public void setStatoSubOrdine(StatoSubOrdine statoSubOrdine) {
        this.statoSubOrdine = statoSubOrdine;
        this.dataAggiornamento = new Date();
    }

    public Date getDataCreazione() {
        return dataCreazione;
    }

    public void setDataCreazione(Date dataCreazione) {
        this.dataCreazione = dataCreazione;
    }

    public Date getDataAggiornamento() {
        return dataAggiornamento;
    }

    public void setDataAggiornamento(Date dataAggiornamento) {
        this.dataAggiornamento = dataAggiornamento;
    }

    public String getNumeroTracking() {
        return numeroTracking;
    }

    public void setNumeroTracking(String numeroTracking) {
        this.numeroTracking = numeroTracking;
    }

    public String getNoteVenditore() {
        return noteVenditore;
    }

    public void setNoteVenditore(String noteVenditore) {
        this.noteVenditore = noteVenditore;
    }

    /**
     * Aggiunge una riga al sub-ordine e aggiorna l'importo totale
     */
    public void aggiungiRiga(RigaSubOrdine riga) {
        this.righeSubOrdine.add(riga);
        riga.setSubOrdine(this);
        this.importoSubOrdine += riga.getPrezzoUnitario() * riga.getQuantitaOrdinata();
    }

    /**
     * Rimuove una riga dal sub-ordine e aggiorna l'importo totale
     */
    public void rimuoviRiga(RigaSubOrdine riga) {
        if (this.righeSubOrdine.remove(riga)) {
            this.importoSubOrdine -= riga.getPrezzoUnitario() * riga.getQuantitaOrdinata();
            riga.setSubOrdine(null);
        }
    }

    /**
     * Ricalcola l'importo totale del sub-ordine
     */
    public void ricalcolaImporto() {
        this.importoSubOrdine = righeSubOrdine.stream()
                .mapToDouble(riga -> riga.getPrezzoUnitario() * riga.getQuantitaOrdinata())
                .sum();
    }

    /**
     * Verifica se il sub-ordine può essere processato dal venditore
     */
    public boolean puoEssereProcessato() {
        return statoSubOrdine == StatoSubOrdine.ATTESA_LAVORAZIONE;
    }

    /**
     * Verifica se il sub-ordine è completato (consegnato)
     */
    public boolean isCompletato() {
        return statoSubOrdine == StatoSubOrdine.CONSEGNATO;
    }

    /**
     * Avanza il sub-ordine al prossimo stato logico
     */
    public boolean avanzaStato() {
        switch (statoSubOrdine) {
            case ATTESA_LAVORAZIONE:
                setStatoSubOrdine(StatoSubOrdine.IN_LAVORAZIONE);
                return true;
            case IN_LAVORAZIONE:
                setStatoSubOrdine(StatoSubOrdine.PRONTO_PER_SPEDIZIONE);
                return true;
            case PRONTO_PER_SPEDIZIONE:
                setStatoSubOrdine(StatoSubOrdine.SPEDITO);
                return true;
            case SPEDITO:
                setStatoSubOrdine(StatoSubOrdine.CONSEGNATO);
                return true;
            default:
                return false; // Stati finali o non validi
        }
    }

    @Override
    public String toString() {
        return "SubOrdine{" +
                "idSubOrdine=" + idSubOrdine +
                ", venditore=" + (venditore != null ? venditore.getEmail() : "null") +
                ", statoSubOrdine=" + statoSubOrdine +
                ", importoSubOrdine=" + importoSubOrdine +
                ", numeroRighe=" + (righeSubOrdine != null ? righeSubOrdine.size() : 0) +
                '}';
    }
}

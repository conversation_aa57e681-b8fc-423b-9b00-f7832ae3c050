package it.unicam.cs.ids.piattaforma_agricola_locale.model.ordine;

/**
 * Enum che rappresenta gli stati possibili di un sub-ordine.
 * Ogni venditore gestisce i propri sub-ordini attraverso questi stati.
 */
public enum StatoSubOrdine {

    /**
     * Il sub-ordine è stato creato e attende di essere preso in carico dal
     * venditore
     */
    ATTESA_LAVORAZIONE("In attesa di lavorazione"),

    /**
     * Il venditore ha iniziato a lavorare sui prodotti del sub-ordine
     */
    IN_LAVORAZIONE("In lavorazione"),

    /**
     * I prodotti sono pronti e il sub-ordine può essere spedito
     */
    PRONTO_PER_SPEDIZIONE("Pronto per spedizione"),

    /**
     * Il sub-ordine è stato spedito dal venditore
     */
    SPEDITO("Spedito"),

    /**
     * Il sub-ordine è stato consegnato al cliente
     */
    CONSEGNATO("Consegnato"),

    /**
     * Il sub-ordine è stato annullato
     */
    ANNULLATO("Annullato");

    private final String descrizione;

    StatoSubOrdine(String descrizione) {
        this.descrizione = descrizione;
    }

    public String getDescrizione() {
        return descrizione;
    }

    /**
     * Verifica se lo stato è finale (non può più cambiare)
     */
    public boolean isStatoFinale() {
        return this == CONSEGNATO || this == ANNULLATO;
    }

    /**
     * Verifica se il sub-ordine può essere lavorato dal venditore
     */
    public boolean puoEssereLavorato() {
        return this == ATTESA_LAVORAZIONE || this == IN_LAVORAZIONE;
    }

    /**
     * Verifica se il sub-ordine può essere spedito
     */
    public boolean puoEssereSpedito() {
        return this == PRONTO_PER_SPEDIZIONE;
    }

    /**
     * Restituisce il prossimo stato logico nella sequenza
     */
    public StatoSubOrdine getProssimoStato() {
        switch (this) {
            case ATTESA_LAVORAZIONE:
                return IN_LAVORAZIONE;
            case IN_LAVORAZIONE:
                return PRONTO_PER_SPEDIZIONE;
            case PRONTO_PER_SPEDIZIONE:
                return SPEDITO;
            case SPEDITO:
                return CONSEGNATO;
            default:
                return this; // Stati finali rimangono invariati
        }
    }

    @Override
    public String toString() {
        return descrizione;
    }
}

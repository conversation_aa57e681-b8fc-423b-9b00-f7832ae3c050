# Database configuration (H2 in-memory for development)
spring.datasource.url=jdbc:h2:file:./data/piattaforma_agricola
spring.datasource.driverClassName=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=password

# H2 Console (for development/testing)
spring.h2.console.enabled=true
spring.h2.console.path=/h2-console

# JPA/Hibernate configuration
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true

# Logging
logging.level.it.unicam.cs.ids.piattaforma_agricola_locale=DEBUG
logging.level.org.springframework.web=DEBUG
logging.level.org.hibernate.SQL=DEBUG

# Application properties
server.port=8080
spring.application.name=piattaforma-agricola-locale

#AUTENTICAZIONE

# Chiave Segreta JWT, generata casualmente
jwt.secret.key=4D6251655468576D5A7134743777217A25432A462D4A614E645267556B587032

# Durata di scadenza dei token in millisecondi
# Access Token: 8 ore (28800000 ms)
jwt.expiration.access-token=28800000

# ========================================
# PERFORMANCE & MONITORING CONFIGURATION
# ========================================

# Spring Boot Actuator Configuration
management.endpoints.web.exposure.include=health,info,metrics,prometheus,caches
management.endpoints.web.base-path=/actuator
management.endpoint.health.show-details=when-authorized
management.endpoint.health.probes.enabled=true
management.health.livenessstate.enabled=true
management.health.readinessstate.enabled=true

# Security per actuator endpoints
management.endpoints.web.cors.allowed-origins=*
management.endpoints.web.cors.allowed-methods=GET,POST

# ========================================
# FASE 4: API DOCUMENTATION CONFIGURATION
# ========================================

# SpringDoc OpenAPI Configuration
springdoc.api-docs.path=/v3/api-docs
springdoc.swagger-ui.path=/swagger-ui.html
springdoc.swagger-ui.enabled=true
springdoc.api-docs.enabled=true

# Swagger UI Customization
springdoc.swagger-ui.operationsSorter=method
springdoc.swagger-ui.tagsSorter=alpha
springdoc.swagger-ui.tryItOutEnabled=true
springdoc.swagger-ui.filter=true
springdoc.swagger-ui.showExtensions=true
springdoc.swagger-ui.showCommonExtensions=true

# Packages to scan for API documentation
springdoc.packages-to-scan=it.unicam.cs.ids.piattaforma_agricola_locale.controller

# Show all APIs together (removed group configuration to show all APIs in one view)

# Metrics Configuration
management.metrics.enable.all=true
management.prometheus.metrics.export.enabled=true

# Cache Configuration
spring.cache.type=caffeine
spring.cache.cache-names=products,companies,events,packages,users
spring.cache.caffeine.spec=maximumSize=1000,expireAfterAccess=600s,expireAfterWrite=1800s

# Database Performance Optimization
spring.jpa.properties.hibernate.jdbc.batch_size=20
spring.jpa.properties.hibernate.order_inserts=true
spring.jpa.properties.hibernate.order_updates=true
spring.jpa.properties.hibernate.jdbc.batch_versioned_data=true

# Connection Pool Optimization (HikariCP)
spring.datasource.hikari.maximum-pool-size=20
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.idle-timeout=300000
spring.datasource.hikari.connection-timeout=30000
spring.datasource.hikari.max-lifetime=1800000

# Application Info per Actuator
info.app.name=@project.name@
info.app.version=@project.version@
info.app.description=Piattaforma Agricola Locale - E-commerce Platform
info.app.encoding=@project.build.sourceEncoding@
info.app.java.version=@java.version@

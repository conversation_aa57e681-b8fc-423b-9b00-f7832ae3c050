# Sub-Ordini: Soluzione al Problema di Gestione Multi-Venditore

## 🎯 Problema Identificato

Il sistema precedente aveva un problema critico: quando un venditore faceva l'API per visualizzare gli ordini ricevuti, vedeva l'intero ordine dell'acquirente con tutti i prodotti, inclusi quelli di altri venditori. Questo creava:

- **Violazione della privacy**: Venditori vedevano prodotti di concorrenti
- **Confusione**: Non era chiaro quali elementi dovesse processare ogni venditore  
- **Coordinamento**: Impossibile sapere quando un ordine era pronto per la spedizione

## 🏗️ Soluzione Implementata: Pattern dei Sub-Ordini

### Architettura

```
Ordine Principale (Acquirente)
    ├── SubOrdine 1 (Produttore A)
    ├── SubOrdine 2 (Trasformatore B)  
    └── SubOrdine 3 (Distributore C)
```

### Componenti Principali

#### 1. **SubOrdine** - Entità Principale

```java
@Entity
public class SubOrdine {
    private Long idSubOrdine;
    private Ordine ordinePrincipale;     // Riferimento all'ordine originale
    private Venditore venditore;         // Il venditore specifico
    private List<RigaSubOrdine> righe;   // Solo i suoi prodotti
    private StatoSubOrdine stato;        // Stato indipendente
    private double importoSubOrdine;     // Importo parziale
    private String numeroTracking;       // Tracking per spedizione
}
```

#### 2. **StatoSubOrdine** - Stati Indipendenti

```java
public enum StatoSubOrdine {
    ATTESA_LAVORAZIONE,     // Creato, attende il venditore
    IN_LAVORAZIONE,         // Venditore sta lavorando
    PRONTO_PER_SPEDIZIONE,  // Pronto per essere spedito
    SPEDITO,                // Spedito dal venditore
    CONSEGNATO,             // Consegnato al cliente
    ANNULLATO               // Annullato
}
```

#### 3. **RigaSubOrdine** - Righe Specifiche

Ogni riga contiene solo i prodotti del venditore specifico.

## 🔄 Workflow Completo

### 1. Creazione Ordine

```java
// Acquirente crea ordine con prodotti di più venditori
POST /api/ordini
{
    "metodoPagamento": "CARTA_CREDITO"
}

// Sistema automaticamente:
// 1. Crea ordine principale
// 2. Raggruppa prodotti per venditore  
// 3. Crea un SubOrdine per ogni venditore
// 4. Ogni SubOrdine contiene solo i prodotti di quel venditore
```

### 2. Gestione da Parte dei Venditori

```java
// Venditore vede SOLO i suoi sub-ordini
GET /api/sub-ordini/venditori

// Venditore processa il suo sub-ordine
PUT /api/sub-ordini/{id}/stato
{
    "nuovoStato": "IN_LAVORAZIONE"
}

// Venditore spedisce e aggiunge tracking
PUT /api/sub-ordini/{id}/tracking
{
    "numeroTracking": "ABC123456"
}
```

### 3. Vista dell'Acquirente

```java
// Acquirente vede l'ordine completo + stato sub-ordini
GET /api/ordini/{id}
// Risposta include:
{
    "idOrdine": 123,
    "numeroSubOrdini": 3,
    "percentualeCompletamento": 66.67,
    "tuttiSubOrdiniCompletati": false
}

// Dettagli specifici dei sub-ordini
GET /api/ordini/{id}/sub-ordini
```

## 🚀 Vantaggi della Soluzione

### ✅ **Separazione delle Responsabilità**

- Ogni venditore vede solo i propri prodotti
- Gestione indipendente degli stati
- Privacy tra venditori garantita

### ✅ **Spedizioni Multiple Realistiche**

- Ogni venditore spedisce i propri prodotti
- Tracking indipendente per ogni spedizione
- Riflette il comportamento di marketplace reali (Amazon, eBay)

### ✅ **Coordinamento Automatico**

- L'ordine principale è "completato" quando tutti i sub-ordini sono consegnati
- Calcolo automatico della percentuale di completamento
- Notifiche coordinate per l'acquirente

### ✅ **Scalabilità**

- Facile aggiungere nuovi venditori
- Pattern Observer per coordinamento
- Facilmente estensibile

## 📋 API Endpoints

### Per i Venditori

```http
GET    /api/sub-ordini/venditori              # Lista sub-ordini del venditore
GET    /api/sub-ordini/{id}                   # Dettagli sub-ordine specifico
PUT    /api/sub-ordini/{id}/stato             # Aggiorna stato
PUT    /api/sub-ordini/{id}/avanza            # Avanza al prossimo stato
PUT    /api/sub-ordini/{id}/tracking          # Aggiungi numero tracking
```

### Per gli Acquirenti (estensioni esistenti)

```http
GET    /api/ordini/{id}                       # Include info sub-ordini
GET    /api/ordini/{id}/sub-ordini            # Dettagli sub-ordini dell'ordine
```

## 🔍 Esempio Pratico

### Scenario: Ordine con 3 Venditori

1. **Acquirente** ordina:
   - 2 pomodori da **Produttore A**
   - 1 pacchetto di pasta da **Trasformatore B**  
   - 1 bottiglia di vino da **Distributore C**

2. **Sistema** crea automaticamente:
   - 1 Ordine principale (€50 totale)
   - 3 SubOrdini separati:
     - SubOrdine 1: Produttore A (€10)
     - SubOrdine 2: Trasformatore B (€15)
     - SubOrdine 3: Distributore C (€25)

3. **Ogni venditore** vede solo il suo:
   - Produttore A vede solo i pomodori
   - Trasformatore B vede solo la pasta
   - Distributore C vede solo il vino

4. **Spedizioni indipendenti**:
   - Produttore A spedisce → tracking ABC123
   - Trasformatore B spedisce → tracking DEF456  
   - Distributore C spedisce → tracking GHI789

5. **Acquirente** riceve:
   - 3 spedizioni separate
   - Aggiornamenti sullo stato di ognuna
   - Ordine completato quando tutte sono consegnate

## 🎓 Benefici per il Progetto Universitario

### **Mostra Buone Pratiche**

- Pattern architetturali (Observer, Strategy)
- Separazione delle responsabilità  
- Design scalabile e mantenibile

### **Realistico**

- Riflette marketplace del mondo reale
- Gestione complessa ma elegante
- Problemi reali con soluzioni professionali

### **Facilmente Testabile**

- Ogni componente è indipendente
- Stati ben definiti e verificabili
- API chiare e documentate

### **Educativo**

- Mostra problem-solving avanzato
- Integrazione di più pattern
- Gestione di complessità distribuita

## 🔧 Implementazione Tecnica

### Database Schema

```sql
-- Tabella sub-ordini
CREATE TABLE sub_ordini (
    id_sub_ordine BIGINT PRIMARY KEY,
    id_ordine_principale BIGINT,
    id_venditore BIGINT,
    stato_sub_ordine VARCHAR(50),
    importo_sub_ordine DECIMAL(10,2),
    data_creazione TIMESTAMP,
    numero_tracking VARCHAR(100)
);

-- Tabella righe sub-ordine  
CREATE TABLE righe_sub_ordine (
    id_riga_sub_ordine BIGINT PRIMARY KEY,
    id_sub_ordine BIGINT,
    tipo_acquistabile VARCHAR(50),
    id_acquistabile BIGINT,
    nome_prodotto VARCHAR(255),
    quantita_ordinata INT,
    prezzo_unitario DECIMAL(10,2)
);
```

### Servizi Implementati

- `SubOrdineService`: Gestione logica sub-ordini
- `SubOrdineController`: API per venditori
- `SubOrdineMapper`: Conversione DTO
- Repository JPA con query ottimizzate

Questa soluzione risolve elegantemente il problema originale mantenendo la complessità gestibile e mostrando pratiche professionali di software engineering.
